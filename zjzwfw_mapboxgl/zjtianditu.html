<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>缩放到范围</title>

    <link href="https://zhejiang.tianditu.gov.cn/lib/mapbox-gl.css" rel="stylesheet">

    <style>
        html,  body {
            width: 100%;
            height: 100%;
            margin: 0;
        }

        .map {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
        }
    </style>
<style>
    .button {
        position: absolute;
        top: 0;
        right:0;
        z-index: 1;
    }
</style>
</head>

<body>
<div id="map" class="map"></div>
<script src="https://zhejiang.tianditu.gov.cn/lib/mapbox-gl.js"></script>

<button id="button" class="button">缩放至宁波</button>
<script>
    var map = new mapboxgl.Map({
        container: 'map',
        zoom: 12,
        center: [120.15, 30.25],
        renderWorldCopies: false,
        localIdeographFontFamily: "'黑体','san-serif'",
        style: 'https://zhejiang.tianditu.gov.cn/vtiles/styles/tdt/streets.json'
    });

    document.getElementById('button').addEventListener('click', function() {
        map.fitBounds([[120.87, 29], [122.315, 30.472]])
    })
</script>
</body>

</html>