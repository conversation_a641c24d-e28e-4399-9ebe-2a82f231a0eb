{"version": 8, "metadata": {}, "sources": {"osm": {"type": "vector", "tiles": ["https://ditu.zjzwfw.gov.cn/vtiles/maps/tdt_main/{z}/{x}/{y}.mvt?v=2025"], "bounds": [118.022529663, 26.9995817330001, 123.249888933, 31.1824714530001]}, "tdt-zj": {"type": "raster", "tiles": ["https://ditu.zjzwfw.gov.cn/services/wmts/imgmap/default/oss?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image%2Fjpgpng&TileMatrix={z}&TileCol={x}&TileRow={y}"], "tileSize": 256, "minzoom": 6, "maxzoom": 20, "bounds": [111.390883, 23.780958, 129.813606, 34.911582]}}, "sprite": "https://zhejiang.tianditu.gov.cn/vtiles/icons/img", "glyphs": "https://zhejiang.tianditu.gov.cn/vtiles/fonts/{fontstack}/{range}.pbf", "layers": [{"id": "zb", "type": "background", "paint": {"background-color": "rgb(170,198,238)"}}, {"id": "vB", "type": "raster", "source": "tdt-zj", "minzoom": 6}, {"id": "og", "type": "line", "source": "osm", "source-layer": "9m", "minzoom": 7, "filter": ["all", ["==", "9m", "630201"]], "paint": {"line-color": "rgb(150,150,150)", "line-dasharray": [30, 10, 10, 10, 10, 10], "line-width": 0.4}}, {"id": "Ax", "type": "line", "source": "osm", "source-layer": "9m", "minzoom": 7, "maxzoom": 14, "filter": ["all", ["==", "9m", "640201"]], "paint": {"line-color": "rgb(150,150,150)", "line-width": 0.4, "line-dasharray": [30, 10, 10, 10, 10, 10]}}, {"id": "8R", "type": "line", "source": "osm", "source-layer": "9m", "minzoom": 9, "maxzoom": 14, "filter": ["all", ["==", "9m", "650201"]], "paint": {"line-color": "rgb(150,150,150)", "line-width": 0.4, "line-dasharray": [30, 10, 10, 10, 10, 10]}}, {"id": "P9", "type": "line", "source": "osm", "source-layer": "9m", "minzoom": 9, "maxzoom": 14, "filter": ["all", ["==", "9m", "660201"]], "paint": {"line-color": "rgb(150,150,150)", "line-width": 0.4, "line-dasharray": [30, 10, 10, 10, 10, 10]}}, {"id": "b9", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["in", "9m", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[14, 4.6], [15, 6], [16, 8], [17, 9]]}}}, {"id": "7Y", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["in", "9m", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[14, 1], [15, 3], [16, 3], [17, 4]]}}}, {"id": "4v", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 12, "filter": ["all", ["in", "9m", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[12, 4.4], [13, 5], [14, 6], [15, 7], [16, 9], [17, 10]]}}}, {"id": "lx", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["==", "9m", "420301"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 2.6], [13, 3], [14, 3.6], [15, 4.6], [16, 5.5], [17, 5.6]]}}}, {"id": "kR", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["==", "9m", "420301"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 3.4], [11, 4.4], [12, 5], [13, 6], [14, 7], [15, 8], [16, 10], [17, 11]]}}}, {"id": "JB", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["==", "9m", "420201"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 2.6], [13, 3.6], [15, 4.6], [16, 5.6], [17, 6.6]]}}}, {"id": "2Z", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["==", "9m", "420201"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 4.6], [11, 5.4], [12, 7], [13, 8], [14, 9], [15, 10], [16, 11], [17, 12]]}}}, {"id": "we", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["==", "9m", "420101"], ["in", "og", "03", "05", "0b", "12", "16"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 2.6], [12, 3.6], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 7.6]]}}}, {"id": "Kx", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["==", "9m", "420101"], ["!in", "og", "03", "05", "0b", "12", "16"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 7.8], [13, 8.8], [14, 9], [15, 11], [16, 12], [17, 14]]}}}, {"id": "Nr", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "9m", "430201", "430300"], ["in", "og", "03", "05", "0b", "15", "16"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 2.6], [12, 3], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 7.6]]}}}, {"id": "G9", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["in", "9m", "430201", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 7.8], [13, 8.8], [14, 9], [15, 11], [16, 12], [17, 14]]}}}, {"id": "MO", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["==", "9m", "420801"], ["in", "og", "03", "05", "06", "07", "0b"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[10, 3.6], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 8]]}}}, {"id": "3X", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["==", "9m", "420801"], ["!in", "og", "03", "05", "06", "07", "0b"], ["==", "zb", "450600"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-dasharray": [4, 2], "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 8], [13, 9], [14, 10], [15, 11], [16, 12], [17, 14]]}}}, {"id": "Rn", "type": "line", "source": "osm", "source-layer": "P9", "minzoom": 10, "layout": {"line-cap": "butt", "line-join": "bevel"}, "paint": {"line-color": "rgba(0,0,0,0.6)", "line-width": 2.2}}, {"id": "ro", "type": "line", "source": "osm", "source-layer": "P9", "minzoom": 10, "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,255,255,0.6)", "line-width": 1.6, "line-dasharray": [10, 10]}}, {"id": "jP", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["==", "zb", "490500"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[14, 4.6], [15, 6], [16, 8], [17, 9]]}}}, {"id": "nX", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["==", "zb", "490500"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[14, 2.6], [15, 4], [16, 6], [17, 7]]}}}, {"id": "ak", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["in", "zb", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[14, 4.6], [15, 6], [16, 8], [17, 9]]}}}, {"id": "q4", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[14, 1], [15, 3], [16, 3], [17, 4]]}}}, {"id": "e8", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 12, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[12, 4.4], [13, 5], [14, 6], [15, 7], [16, 9], [17, 10]]}}}, {"id": "VM", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420301", "420302"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 2.6], [13, 3], [14, 3.6], [15, 4.6], [16, 5.5], [17, 5.6]]}}}, {"id": "<PERSON>j", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420301", "420302"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 3.4], [11, 4.4], [12, 5], [13, 6], [14, 7], [15, 8], [16, 10], [17, 11]]}}}, {"id": "6Q", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "420201", "420202"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 2.6], [13, 3.6], [15, 4.6], [16, 5.6], [17, 6.6]]}}}, {"id": "XY", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "420201", "420202"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 4.6], [11, 5.4], [12, 7], [13, 8], [14, 9], [15, 10], [16, 11], [17, 12]]}}}, {"id": "QV", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "420101", "420102"], ["in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 2.6], [12, 3.6], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 7.6]]}}}, {"id": "gZ", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["in", "zb", "420101", "420102"], ["!in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 7.8], [13, 8.8], [14, 9], [15, 11], [16, 12], [17, 14]]}}}, {"id": "1r", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["any", ["all", ["in", "zb", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"]], ["all", ["in", "9m", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[14, 3], [15, 4], [16, 6], [17, 7]]}}}, {"id": "OP", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 14, "filter": ["any", ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[14, 1], [15, 2], [16, 2], [17, 3]]}}}, {"id": "59", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 12, "filter": ["any", ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[12, 2.4], [13, 3], [14, 4], [15, 5], [16, 7], [17, 8]]}}}, {"id": "Ba", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420301", "420302"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420301", "420302"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[10, 1], [13, 1.4], [14, 2], [15, 3], [16, 3.5], [17, 3.6]]}}}, {"id": "WG", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420301", "420302"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420301", "420302"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[10, 1.4], [11, 2.4], [12, 3], [13, 4], [14, 5], [15, 6], [16, 8], [17, 9]]}}}, {"id": "DG", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420201", "420202"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420201", "420202"], ["in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[10, 1], [13, 2], [15, 3], [16, 4], [17, 5]]}}}, {"id": "ZP", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420201", "420202"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "9m", "420201", "420202"], ["!in", "og", "05", "0b", "12", "15", "16", "17"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[10, 2.6], [11, 3.4], [12, 5], [13, 6], [14, 7], [15, 8], [16, 9], [17, 10]]}}}, {"id": "xw", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420101", "420102"], ["in", "og", "03", "05", "0b", "12", "16"]], ["all", ["in", "9m", "420101", "420102"], ["in", "og", "03", "05", "0b", "12", "16"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[10, 1], [12, 2], [13, 3], [14, 4], [15, 4.5], [16, 5], [17, 6]]}}}, {"id": "0w", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 7, "filter": ["any", ["all", ["in", "zb", "420101", "420102"], ["!in", "og", "03", "05", "0b", "12", "16"]], ["all", ["in", "9m", "420101", "420102"], ["!in", "og", "03", "05", "0b", "12", "16"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[7, 1], [10, 4], [11, 5], [12, 5.8], [13, 6.8], [14, 7], [15, 9], [16, 10], [17, 12]]}}}, {"id": "ya", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "430201", "430202", "430300"], ["in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 2.6], [12, 3], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 7.6]]}}}, {"id": "mo", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["in", "zb", "430201", "430202", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 7.8], [13, 8.8], [14, 9], [15, 11], [16, 12], [17, 14]]}}}, {"id": "90m", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "430201", "430202", "430300"], ["in", "og", "03", "05", "0b", "15", "16"]], ["all", ["in", "9m", "430201", "430202", "430300"], ["in", "og", "03", "05", "0b", "15", "16"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[10, 1], [12, 1.4], [13, 3], [14, 4], [15, 4.5], [16, 5], [17, 6]]}}}, {"id": "zbb", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["any", ["all", ["in", "zb", "430201", "430202", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"]], ["all", ["in", "9m", "430201", "430202", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"], ["==", "zb", "450600"]]], "layout": {"line-cap": "round", "line-join": "bevel"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[9, 1], [10, 4], [11, 5], [12, 5.8], [13, 6.8], [14, 7], [15, 9], [16, 10], [17, 12]]}}}, {"id": "vxB", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "420801", "420802"], ["in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[10, 3.6], [13, 4.6], [14, 5.6], [15, 6.1], [16, 6.6], [17, 8]]}}}, {"id": "oog", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["in", "zb", "420801", "420802"], ["!in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[9, 2], [10, 6], [11, 7], [12, 8], [13, 9], [14, 10], [15, 11], [16, 12], [17, 14]]}}}, {"id": "A9x", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420801", "420802"], ["in", "og", "03", "05", "06", "07", "0b"]], ["all", ["in", "9m", "420801", "420802"], ["in", "og", "03", "05", "06", "07", "0b"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(186,146,241,0.4)", "line-width": {"stops": [[10, 2], [13, 3], [14, 4], [15, 4.5], [16, 5], [17, 6]]}}}, {"id": "83R", "type": "line", "source": "osm", "source-layer": "Ax", "minzoom": 7, "filter": ["any", ["all", ["in", "zb", "420801", "420802"], ["!in", "og", "03", "05", "06", "07", "0b"]], ["all", ["in", "9m", "420801", "420802"], ["!in", "og", "03", "05", "06", "07", "0b"], ["==", "zb", "450600"]]], "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(186,146,241,0.4)", "line-width": {"stops": [[7, 1], [10, 4], [11, 5], [12, 6], [13, 7], [14, 8], [15, 9], [16, 10], [17, 12]]}}}, {"id": "PZ9", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 8], [17, 9]]}}}, {"id": "bD9", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[16, 6], [17, 7]]}}}, {"id": "7MY", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 3], [17, 4]]}}}, {"id": "4Wv", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[16, 2], [17, 3]]}}}, {"id": "lPx", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 9], [17, 10]]}}}, {"id": "kKR", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,255,255,0.4)", "line-width": {"stops": [[16, 7], [17, 8]]}}}, {"id": "JlB", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["any", ["all", ["==", "zb", "420301"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 5.5], [17, 5.6]]}}}, {"id": "25Z", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["any", ["all", ["==", "zb", "420301"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[16, 3.5], [17, 3.6]]}}}, {"id": "wDe", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["any", ["all", ["==", "zb", "420301"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 10], [17, 11]]}}}, {"id": "<PERSON><PERSON>", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["any", ["all", ["==", "zb", "420301"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[16, 8], [17, 9]]}}}, {"id": "NKr", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420201"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 5.6], [17, 6.6]]}}}, {"id": "GJ9", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420201"], ["in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[16, 4], [17, 5]]}}}, {"id": "MDO", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420201"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 11], [17, 12]]}}}, {"id": "33X", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420201"], ["!in", "og", "05", "0b", "12", "15", "16", "17"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(255,244,140,0.4)", "line-width": {"stops": [[16, 9], [17, 10]]}}}, {"id": "Rln", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420101"], ["in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 6.6], [17, 7.6]]}}}, {"id": "rko", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420101"], ["in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[16, 5], [17, 6]]}}}, {"id": "jgP", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420101"], ["!in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 12], [17, 14]]}}}, {"id": "n6X", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420101"], ["!in", "og", "03", "05", "0b", "12", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[16, 10], [17, 12]]}}}, {"id": "a6k", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "430201", "430300"], ["in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 6.6], [17, 7.6]]}}}, {"id": "qB4", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "430201", "430300"], ["in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[16, 5], [17, 6]]}}}, {"id": "e68", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "430201", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 12], [17, 14]]}}}, {"id": "V6M", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["in", "zb", "430201", "430300"], ["!in", "og", "03", "05", "0b", "15", "16"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(254,205,110,0.4)", "line-width": {"stops": [[17, 10], [17, 12]]}}}, {"id": "YDj", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420801"], ["in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 6.6], [17, 8]]}}}, {"id": "6wQ", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420801"], ["in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(186,146,241,0.4)", "line-width": {"stops": [[16, 5], [17, 6]]}}}, {"id": "XeY", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420801"], ["!in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(0,0,0,0.4)", "line-width": {"stops": [[16, 12], [17, 14]]}}}, {"id": "QyV", "type": "line", "source": "osm", "source-layer": "8R", "minzoom": 16, "filter": ["all", ["==", "zb", "420801"], ["!in", "og", "03", "05", "06", "07", "0b"]], "layout": {"line-join": "bevel", "line-cap": "butt"}, "paint": {"line-color": "rgba(186,146,241,0.4)", "line-width": {"stops": [[16, 10], [17, 12]]}}}, {"id": "g9Z", "type": "fill", "source": "osm", "source-layer": "7Y", "minzoom": 16, "paint": {"fill-color": ["concat", "rgb(", ["get", "og"], ")"], "fill-opacity": 0.4}}, {"id": "1Dr", "type": "line", "source": "osm", "source-layer": "b9", "minzoom": 10, "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": "rgba(255,255,255,0.75)", "line-width": {"stops": [[10, 3.4], [11, 3.5], [12, 4]]}}}, {"id": "OmP", "type": "line", "source": "osm", "source-layer": "b9", "minzoom": 10, "layout": {"line-join": "bevel", "line-cap": "round"}, "paint": {"line-color": ["concat", "rgb(", ["get", "og"], ")"], "line-width": {"stops": [[10, 1.4], [11, 1.5], [12, 2]]}, "line-opacity": 0.6}}, {"id": "529", "type": "symbol", "source": "osm", "source-layer": "og", "minzoom": 10, "layout": {"symbol-placement": "line-center", "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 13}, "paint": {"text-color": "rgb(14,122,173)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "Bva", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 15, "filter": ["any", ["all", ["in", "zb", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"]], ["all", ["in", "9m", "440100", "440200", "440300", "430501", "430502", "430503", "430400", "430600", "440400", "440600", "420900"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 500, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12, "text-letter-spacing": 0.3}, "paint": {"text-color": "rgb(40,40,40)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"stops": [[15, 1], [16, 2]]}}}, {"id": "WbG", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 15, "filter": ["any", ["all", ["in", "zb", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["!in", "og", "03", "04", "05", "08", "0a", "0b", "0f", "12", "15", "16", "17"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["!in", "vB", "1", "2", "3", "4"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 500, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12, "text-letter-spacing": 0.3}, "paint": {"text-color": "rgb(40,40,40)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"stops": [[15, 1], [15, 2]]}}}, {"id": "DoG", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 13, "filter": ["any", ["all", ["in", "zb", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["!in", "og", "03", "04", "05", "08", "0a", "0b", "0f", "12", "15", "16", "17"]], ["all", ["in", "9m", "420400", "420402", "420500"], ["in", "vB", "1", "2", "3", "4"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 500, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12, "text-letter-spacing": 0.3}, "paint": {"text-color": "rgb(104,54,13)", "text-halo-color": "rgb(253,245,220)", "text-halo-width": {"stops": [[13, 1], [16, 2]]}}}, {"id": "ZMP", "type": "symbol", "source": "osm", "source-layer": "Kx", "minzoom": 17, "layout": {"symbol-placement": "point", "text-field": "{stylename}", "text-size": 12}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "x8w", "type": "symbol", "source": "osm", "source-layer": "we", "minzoom": 17, "layout": {"symbol-placement": "point", "text-field": "{stylename}", "text-size": 12}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "0gw", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 15, "filter": ["all", ["in", "9m", "150102", "210212", "230208", "230209", "230215", "230222", "230223"]], "layout": {"symbol-placement": "point", "symbol-sort-key": ["-", 10, ["to-number", ["get", "importance"]]], "icon-image": "{stylename}", "icon-size": 2, "icon-padding": 10}}, {"id": "yPa", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 10, "filter": ["all", ["!in", "9m", "190101", "190102", "190103", "190104", "190105", "190106", "190107", "190108", "190109", "190110", "190111", "260104", "260105", "260106", "260107", "260108", "260109", "260110", "260111", "260112", "260113", "260114", "260115", "260116", "260117", "150102", "210212", "230208", "230209", "230215", "230222", "230223", "230105", "230106", "260300", "260304"]], "layout": {"symbol-placement": "point", "symbol-sort-key": ["-", 10, ["to-number", ["get", "importance"]]], "text-field": ["case", [">", ["length", ["get", "name"]], 12], "", ["get", "name"]], "text-size": 12, "text-padding": 10, "text-radial-offset": 0, "text-variable-anchor": ["left", "right", "top", "bottom"], "text-justify": "auto", "text-max-width": ["max", ["ceil", ["/", ["length", ["get", "name"]], 2]], 8], "icon-image": ["case", [">", ["length", ["get", "name"]], 12], "", ["get", "stylename"]], "icon-size": 2}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "mNo", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 10, "filter": ["all", ["in", "9m", "190101", "190102", "190103", "190104", "190105", "190106", "190107", "190108", "190109", "190110", "190111"]], "layout": {"symbol-placement": "point", "symbol-sort-key": ["-", 10, ["to-number", ["get", "importance"]]], "text-field": ["case", [">", ["length", ["get", "name"]], 12], "", ["get", "name"]], "text-size": 12, "text-offset": [0.6, 0], "text-padding": {"stops": [[10, 30], [17, 10]]}, "text-justify": "left", "text-anchor": "left", "text-max-width": ["max", ["ceil", ["/", ["length", ["get", "name"]], 2]], 8], "icon-image": ["case", [">", ["length", ["get", "name"]], 12], "", ["get", "stylename"]], "icon-size": 2}, "paint": {"text-color": "rgb(40,40,40)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "9km", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 12, "filter": ["any", ["all", ["in", "zb", "420301", "420302"], ["!in", "og", "03", "04", "05", "08", "0a", "0b", "0f", "12", "15", "16", "17"]], ["all", ["in", "9m", "420301", "420302"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12, "text-letter-spacing": 0.3}, "paint": {"text-color": "rgb(104,54,13)", "text-halo-color": "rgb(253,245,220)", "text-halo-width": {"stops": [[12, 1], [16, 2]]}}}, {"id": "zRb", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 11, "filter": ["any", ["all", ["in", "zb", "420201", "420202"], ["!in", "og", "03", "04", "05", "08", "0a", "0b", "0f", "12", "15", "16", "17"]], ["all", ["in", "9m", "420201", "420202"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12, "text-letter-spacing": 0.5}, "paint": {"text-color": "rgb(104,54,13)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"stops": [[11, 1], [12, 2]]}}}, {"id": "vBB", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420101", "420102"], ["!in", "og", "03", "04", "05", "08", "0b", "0f", "12", "15", "16", "17"]], ["all", ["in", "9m", "420101", "420102"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": {"stops": [[10, 13], [14, 14]]}, "text-letter-spacing": 0.5}, "paint": {"text-color": "rgb(70,35,7)", "text-halo-color": "rgb(255,222,102)", "text-halo-width": {"stops": [[10, 1], [14, 2]]}}}, {"id": "oDg", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "430201", "430202", "430300"], ["!in", "og", "03", "05", "08", "0b", "0f", "16"]], ["all", ["in", "9m", "430201", "430202", "430300"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": {"stops": [[10, 13], [13, 14]]}, "text-letter-spacing": 0.5}, "paint": {"text-color": "rgb(70,35,7)", "text-halo-color": "rgb(255,222,102)", "text-halo-width": {"stops": [[10, 1], [13, 2]]}}}, {"id": "Amx", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["any", ["all", ["in", "zb", "420801", "420802"], ["!in", "og", "03", "05", "08", "0b", "0f", "16"]], ["all", ["in", "9m", "420801", "420802"], ["==", "zb", "450600"]]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": {"stops": [[10, 13], [11, 14]]}, "text-letter-spacing": 0.5}, "paint": {"text-color": "rgb(123,88,190)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"stops": [[10, 1], [11, 2]]}, "text-translate-anchor": "viewport"}}, {"id": "8VR", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 10, "filter": ["all", ["in", "zb", "420201", "420202"], ["!in", "og", "03", "04", "05", "08", "0a", "0b", "0f", "12", "15", "16", "17"], ["!=", "routenum", ""], ["has", "routenum"]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{routenum}", "text-size": 9, "text-letter-spacing": 0.4, "text-rotation-alignment": "viewport", "icon-image": "shengdao1", "icon-text-fit": "both", "icon-text-fit-padding": [2, 2, 2, 2], "icon-rotation-alignment": "viewport"}, "paint": {"text-color": "rgb(255,255,255)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 0.2}}, {"id": "PN9", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 9, "filter": ["all", ["in", "zb", "420101", "420102"], ["!in", "og", "03", "04", "05", "08", "0b", "0f", "12", "15", "16", "17"], ["!=", "routenum", ""], ["has", "routenum"]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{routenum}", "text-size": 9, "text-letter-spacing": 0.4, "text-rotation-alignment": "viewport", "icon-image": "guodao1", "icon-text-fit": "both", "icon-text-fit-padding": [2, 2, 2, 2], "icon-rotation-alignment": "viewport"}, "paint": {"text-color": "rgb(255,255,255)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 0.2}}, {"id": "b19", "type": "symbol", "source": "osm", "source-layer": "Ax", "minzoom": 8, "filter": ["all", ["in", "zb", "420801", "420802"], ["!in", "og", "03", "05", "08", "0b", "0f", "16"], ["!=", "routenum", ""], ["has", "routenum"]], "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{routenum}", "text-size": 10, "text-letter-spacing": 0.4, "text-rotation-alignment": "viewport", "icon-image": "gaosua1", "icon-text-fit": "both", "icon-text-fit-padding": [2, 4, 2, 4], "icon-rotation-alignment": "viewport"}, "paint": {"text-color": "rgb(255,255,255)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 0.2}}, {"id": "7nY", "type": "symbol", "source": "osm", "source-layer": "b9", "minzoom": 12, "layout": {"symbol-placement": "line", "symbol-spacing": 900, "symbol-avoid-edges": true, "text-field": "{name}", "text-size": 12.5, "text-letter-spacing": 0.2}, "paint": {"text-color": "rgb(80,80,80)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 2}}, {"id": "4Qv", "type": "symbol", "source": "osm", "source-layer": "JB", "minzoom": 12, "filter": ["all", ["in", "9m", "450101", "450102"]], "layout": {"text-field": {"stops": [[12, ""], [14, "{name}"]]}, "text-size": 12, "text-justify": "left", "text-anchor": "left", "text-offset": [0.8, 0], "icon-image": ["case", ["==", ["get", "stylename"], "330100"], "杭州地铁", ["==", ["get", "stylename"], "330200"], "宁波地铁", ["==", ["get", "stylename"], "330300"], "温州地铁", ["==", ["get", "stylename"], "330400"], "嘉兴有轨电车", ["==", ["get", "stylename"], "330600"], "绍兴地铁", ["==", ["get", "stylename"], "330700"], "金华轨道交通", ["==", ["get", "stylename"], "331000"], "台州地铁", ""], "icon-size": {"stops": [[12, 0.75], [13, 1], [14, 1.15]]}}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "lgx", "type": "symbol", "source": "osm", "source-layer": "JB", "minzoom": 16, "filter": ["all", ["==", "9m", "450190"]], "layout": {"text-field": "{name}", "text-size": 12, "icon-image": "地铁口", "icon-text-fit": "both", "icon-text-fit-padding": [2, 4, 2, 4]}, "paint": {"text-color": "rgb(255,255,255)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 0.2}}, {"id": "kAR", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 10, "maxzoom": 18, "filter": ["all", ["in", "9m", "260111", "260112", "260113", "260114", "260115", "260116", "260117"]], "layout": {"symbol-placement": "point", "symbol-sort-key": ["-", 10, ["to-number", ["get", "importance"]]], "text-field": "{name}", "text-size": 10, "text-padding": 50}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "9k7", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 13, "maxzoom": 15, "filter": ["all", ["in", "9m", "260109", "260110"]], "layout": {"text-field": "{name}", "text-offset": [0.5, 0], "text-keep-upright": false, "text-size": 12, "icon-image": "jie4", "icon-text-fit": "both", "icon-text-fit-padding": [1, 4, 3, 4]}, "paint": {"text-color": {"type": "interval", "stops": [[15, "rgb(255,255,255)"], [16, "rgb(40,40,40)"]]}, "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"type": "interval", "stops": [[15, 0.1], [16, 2]]}}}, {"id": "zRa", "type": "symbol", "source": "osm", "source-layer": "2Z", "maxzoom": 13, "filter": ["all", ["in", "9m", "260109", "260110"]], "layout": {"text-field": "{name}", "text-size": 11, "text-padding": 10}, "paint": {"text-color": "rgb(82,73,73)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "vBV", "type": "symbol", "source": "osm", "source-layer": "2Z", "minzoom": 10, "maxzoom": 13, "filter": ["all", ["==", "9m", "260108"]], "layout": {"text-field": "{name}", "text-offset": [0.5, 0], "text-keep-upright": false, "text-size": 12, "icon-image": "qu4", "icon-text-fit": "both", "icon-text-fit-padding": [1, 4, 3, 4]}, "paint": {"text-color": {"type": "interval", "stops": [[13, "rgb(255,255,255)"], [14, "rgb(40,40,40)"]]}, "text-halo-color": "rgb(255,255,255)", "text-halo-width": {"type": "interval", "stops": [[14, 0.1], [15, 2]]}}}, {"id": "oD5", "type": "symbol", "source": "osm", "source-layer": "2Z", "maxzoom": 10, "filter": ["all", ["==", "9m", "260108"]], "layout": {"text-field": "{name}", "text-size": 12}, "paint": {"text-color": "rgb(177,93,38)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 1}}, {"id": "Am5", "type": "symbol", "source": "osm", "source-layer": "2Z", "maxzoom": 12, "filter": ["all", ["in", "9m", "260105", "260107"]], "layout": {"text-field": "{name}", "text-size": 14, "text-offset": [0.5, 0], "text-anchor": "left", "text-justify": "left", "icon-image": "zhengfujiguan1", "icon-size": 2}, "paint": {"text-color": "rgb(179,105,70)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 2}}, {"id": "8Vq", "type": "symbol", "source": "osm", "source-layer": "2Z", "maxzoom": 6, "filter": ["all", ["==", "9m", "260104"]], "layout": {"text-field": "{name}", "text-size": 16, "text-offset": [0.5, 0], "text-anchor": "left", "text-justify": "left", "icon-image": "zhengfujiguan1", "icon-size": 2}, "paint": {"text-color": "rgb(179,105,70)", "text-halo-color": "rgb(255,255,255)", "text-halo-width": 2}}], "id": "3e94085cacf84a0ea47216233a352756"}