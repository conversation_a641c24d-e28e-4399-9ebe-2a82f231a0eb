<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>mapbox示例</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <link href="js/mapbox/mapbox-gl.css" rel="stylesheet" />
    <script src="js/mapbox/mapbox-gl-dev.js"></script>
    <script src="js/mapbox/Texture.min.js"></script>
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      #map {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
      }
      #level {
        position: fixed;
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
        background: #fff;
        color: #000;
        font-size: 16p;
        border-radius: 10px;
        z-index: 1000;
      }
    </style>
  </head>
  <body>
    <div id="level"></div>
    <div id="map"></div>
    <script>
      console.log(mapboxgl)
      mapboxgl.accessToken =
        'pk.eyJ1IjoiemhhbmdjaGVuMTk4OCIsImEiOiJjajYyeHMybzUxZDNlMzBqeHpxY3F4OTI0In0.GEHyHKuNf-hgOckcgOtYNg'
      mapboxgl.isMercator = false
      mapboxgl.workerCount = 4
      var map = new mapboxgl.Map({
        container: 'map',
        // style:
        //   'https://ditu.zjzwfw.gov.cn/mapserver/styleInfo/zjvmap/mapbox_tdt_biaozhunyangshi_2017/all/mapbox/style.json',

        style: {
          version: 8,
          name: 'BaseMap',
          // 定义所有数据源
          sources: {
            // 影像底图的数据源
            'local-basemap-source7': {
              type: 'raster',
              // tiles: [`http://***************:8080/geowebcache/service/wms/{z}/{y}/{x}`],
              tiles: [`/map/l7/{z}/{y}/{x}`],
              // tiles: [`http://localhost:3000/tiles/{z}/{x}/{y}.png`],
              tileSize: 256
            }
          },
          // 定义所有图层，注意数组顺序决定了渲染顺序
          layers: [
            // 影像底图图层 (先渲染，在最下面)
            {
              id: 'local-basemap-layer7',
              type: 'raster',
              source: 'local-basemap-source7' // 引用上面定义的source id
            }
          ]
        },
        center: [120, 30],
        zoom: 9,
        transformRequest: (url, resourceType) => {},
        hash: true
      })

      let texture = new Texture({
        map: this.map,
        mapserverUrl: 'https://ditu.zjzwfw.gov.cn/mapserver',
        styleId: 'mapbox_tdt_biaozhunyangshi_2017',
        serviceName: 'zjvmap'
      })
      map.on('style.load', () => {
        console.log('加载样式成功')
      })

      texture.load(() => {
        console.log('加载符号成功')
      })
    </script>
  </body>
</html>
