<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>切换地图样式</title>

    <link href="https://zhejiang.tianditu.gov.cn/lib/mapbox-gl.css" rel="stylesheet">

    <style>
        html,  body {
            width: 100%;
            height: 100%;
            margin: 0;
        }

        .map {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
        }
    </style>
<style>
    .menu {
        position: absolute;
        background: #fff;
        padding: 10px;
        font-family: 'Open Sans', sans-serif;
        left: 0;
        top: 0;
    }
</style>
</head>

<body>
<div id="map" class="map"></div>
<script src="https://zhejiang.tianditu.gov.cn/lib/mapbox-gl.js"></script>

<div id='menu' class="menu">
    <input id='streets_zw' type='radio' name='rtoggle' value='streets_zw'>
    <label for='streets_zw'>政务</label>
    <input id='streets' type='radio' name='rtoggle' value='streets' checked='checked'>
    <label for='streets'>标准</label>
    <input id='dark' type='radio' name='rtoggle' value='dark'>
    <label for='dark'>夜晚</label>
    <input id='img' type='radio' name='rtoggle' value='img'>
    <label for='img'>影像</label>
</div>
<script>
    var map = new mapboxgl.Map({
        container: 'map',
        zoom: 12,
        center: [120.15, 30.25],
        renderWorldCopies: false,
        localIdeographFontFamily: "'黑体','san-serif'",
        style: 'https://zhejiang.tianditu.gov.cn/vtiles/styles/tdt/img.json'
    });
	
	const geojson = {
        "type": "FeatureCollection",
        "features": [{
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [120.2079, 30.29247]
            },
            "properties": {
                "title": "杭州东站",
                "icon": "火车站"
            }
        }, {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [120.0966, 30.243]
            },
            "properties": {
                "title": "灵隐寺",
                "icon": "寺庙道观"
            }
        }, {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [120.15105, 30.24322]
            },
            "properties": {
                "title": "柳浪闻莺",
                "icon": "风景名胜"
            }
        }]
    };

    map.on('load', function () {
        addLabelLayer();
    });
	
	function addLabelLayer(){
		map.addLayer({
            "id": "points",
            "type": "symbol",
            "source": {
                "type": "geojson",
                "data": geojson
            },
            "layout": {
                "icon-image": "{icon}",
                "icon-size": 2,
                "text-field": "{title}",
                "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
                "text-offset": [0, 0.6],
                "text-anchor": "top"
            }
        });
	}

    var layerList = document.getElementById('menu');
    var inputs = layerList.getElementsByTagName('input');

    function switchLayer(layer) {
        let layerId = layer.target.id;
        map.setStyle('https://zhejiang.tianditu.gov.cn/vtiles/styles/tdt/' + layerId + '.json', {
            localIdeographFontFamily: "'黑体','san-serif'"
        });
		
		setTimeout(() => {
              addLabelLayer();
            }, 500);
    }

    for (let i = 0; i < inputs.length; i++) {
        inputs[i].onclick = switchLayer;
    }
</script>
</body>

</html>